import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:developer' as developer;

import 'models/transaction_model.dart';
import 'services/storage_service.dart';
import 'services/parser/mlkit_parser_service.dart';
import 'services/startup_service.dart';
import 'navigation/app_navigation.dart';
import 'theme.dart';

void main() async {
  final startupStartTime = DateTime.now();
  developer.Timeline.startSync('App Startup');

  WidgetsFlutterBinding.ensureInitialized();

  // Only initialize essential storage service synchronously
  developer.Timeline.startSync('Storage Service Init');
  final storageService = StorageService();
  await storageService.init();
  developer.Timeline.finishSync();

  final storageInitTime = DateTime.now();
  developer.log('Storage service initialized in ${storageInitTime.difference(startupStartTime).inMilliseconds}ms',
      name: 'StartupPerformance');

  // Create startup service for coordinated initialization
  final startupService = StartupService.getInstance(storageService);

  developer.Timeline.finishSync();

  runApp(MultiProvider(
    providers: [
      // Provide startup service for coordination
      ChangeNotifierProvider<StartupService>.value(value: startupService),
      // Provide nullable services initially - they will be populated by StartupService
      Provider<ThemeProvider?>.value(value: null),
      Provider<TransactionProvider?>.value(value: null),
      Provider<MlKitParserService?>.value(value: null),
    ],
    child: const MoneyLoverChatApp(),
  ));

  final uiStartTime = DateTime.now();
  developer.log('UI started in ${uiStartTime.difference(startupStartTime).inMilliseconds}ms',
      name: 'StartupPerformance');
}

class MoneyLoverChatApp extends StatelessWidget {
  const MoneyLoverChatApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Use Consumer to handle nullable ThemeProvider during startup
    return Consumer<StartupService>(
      builder: (context, startupService, child) {
        final themeProvider = startupService.themeProvider;

        return MaterialApp(
          title: 'Money Lover Chat',
          // Use default light theme if ThemeProvider not ready yet
          theme: themeProvider?.themeData ?? ThemeData.light(),
          debugShowCheckedModeBanner: false,
          home: const AppNavigation(),
        );
      },
    );
  }
}