import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/startup_service.dart';

/// Widget that displays during app startup while services are initializing
class StartupLoadingWidget extends StatefulWidget {
  const StartupLoadingWidget({super.key});

  @override
  State<StartupLoadingWidget> createState() => _StartupLoadingWidgetState();
}

class _StartupLoadingWidgetState extends State<StartupLoadingWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    // Setup animations
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));
    
    // Start animations
    _pulseController.repeat(reverse: true);
    _fadeController.forward();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Consumer<StartupService>(
        builder: (context, startupService, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // App logo/icon with pulse animation
                  AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primaryContainer,
                            borderRadius: BorderRadius.circular(60),
                            boxShadow: [
                              BoxShadow(
                                color: theme.colorScheme.primary.withValues(alpha: 0.3),
                                blurRadius: 20,
                                spreadRadius: 5,
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.account_balance_wallet,
                            size: 60,
                            color: theme.colorScheme.onPrimaryContainer,
                          ),
                        ),
                      );
                    },
                  ),
                  
                  const SizedBox(height: 40),
                  
                  // App title
                  Text(
                    'Money Lover Chat',
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  Text(
                    'Your AI-powered expense tracker',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  
                  const SizedBox(height: 60),
                  
                  // Progress indicator
                  SizedBox(
                    width: 200,
                    child: LinearProgressIndicator(
                      backgroundColor: theme.colorScheme.surfaceVariant,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        theme.colorScheme.primary,
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Service initialization status
                  _buildServiceStatus(context, startupService),
                  
                  const SizedBox(height: 40),
                  
                  // Loading tips
                  _buildLoadingTips(context),
                  
                  // Error handling
                  if (startupService.hasAnyServiceFailed)
                    _buildErrorSection(context, startupService),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildServiceStatus(BuildContext context, StartupService startupService) {
    final theme = Theme.of(context);
    final statuses = startupService.serviceStatuses;
    
    return Column(
      children: [
        Text(
          'Initializing services...',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 16),
        ...statuses.entries.map((entry) {
          final serviceName = entry.key;
          final status = entry.value;
          
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _getStatusIcon(status.state, theme),
                const SizedBox(width: 8),
                Text(
                  _getServiceDisplayName(serviceName),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _getStatusIcon(ServiceInitializationState state, ThemeData theme) {
    switch (state) {
      case ServiceInitializationState.pending:
        return Icon(
          Icons.schedule,
          size: 16,
          color: theme.colorScheme.onSurfaceVariant,
        );
      case ServiceInitializationState.loading:
        return SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          ),
        );
      case ServiceInitializationState.ready:
        return Icon(
          Icons.check_circle,
          size: 16,
          color: Colors.green,
        );
      case ServiceInitializationState.failed:
        return Icon(
          Icons.error,
          size: 16,
          color: Colors.red,
        );
    }
  }

  String _getServiceDisplayName(String serviceName) {
    switch (serviceName) {
      case 'theme':
        return 'Theme Service';
      case 'transaction':
        return 'Transaction Service';
      case 'mlkit':
        return 'ML Kit Service';
      default:
        return serviceName;
    }
  }

  Widget _buildLoadingTips(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 32),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            Icons.lightbulb_outline,
            color: theme.colorScheme.primary,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            'Tip: You can type natural language like "spent \$25 on coffee" and the app will automatically categorize your expenses!',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorSection(BuildContext context, StartupService startupService) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 32),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.warning,
            color: Colors.red,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            'Some services failed to initialize',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'The app will continue with limited functionality. You can retry initialization.',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.red.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: 12),
          ElevatedButton(
            onPressed: () => startupService.retryFailedServices(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}
