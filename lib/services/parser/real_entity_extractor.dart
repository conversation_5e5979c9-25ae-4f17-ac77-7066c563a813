import 'package:google_mlkit_entity_extraction/google_mlkit_entity_extraction.dart' as mlkit;
import 'entity_extractor_base.dart';

/// Concrete implementation of EntityAnnotationBase that wraps ML Kit EntityAnnotation
class RealEntityAnnotation implements EntityAnnotationBase {
  final mlkit.EntityAnnotation _mlkitAnnotation;

  RealEntityAnnotation(this._mlkitAnnotation);

  @override
  String get text => _mlkitAnnotation.text;

  @override
  int get start => _mlkitAnnotation.start;

  @override
  int get end => _mlkitAnnotation.end;

  @override
  EntityType get entityType {
    // Convert ML Kit entity type to our abstraction
    // Using runtime type checking as the current implementation does
    final runtimeType = _mlkitAnnotation.runtimeType.toString();
    if (runtimeType.contains('Money')) {
      return EntityType.money;
    } else if (runtimeType.contains('DateTime')) {
      return EntityType.dateTime;
    } else if (runtimeType.contains('Address')) {
      return EntityType.address;
    } else if (runtimeType.contains('Email')) {
      return EntityType.email;
    } else if (runtimeType.contains('Phone')) {
      return EntityType.phone;
    } else if (runtimeType.contains('Url')) {
      return EntityType.url;
    } else {
      return EntityType.other;
    }
  }

  /// Get the original ML Kit annotation for cases where specific ML Kit functionality is needed
  mlkit.EntityAnnotation get mlkitAnnotation => _mlkitAnnotation;
}

/// Concrete implementation of EntityExtractorBase that wraps the actual Google ML Kit EntityExtractor
class RealEntityExtractor implements EntityExtractorBase {
  mlkit.EntityExtractor? _entityExtractor;
  bool _isInitialized = false;

  /// Initialize the ML Kit entity extractor
  /// This handles model downloading and initialization
  Future<void> initialize() async {
    await initializeWithCaching(false);
  }

  /// Initialize the ML Kit entity extractor with caching optimization
  /// [modelWasDownloaded] - Whether the model was previously downloaded (to skip check)
  Future<void> initializeWithCaching(bool modelWasDownloaded) async {
    if (_isInitialized) return;

    try {
      print('Initializing Real ML Kit Entity Extractor with caching...');

      // Skip model download check if we know it was already downloaded
      if (!modelWasDownloaded) {
        // Check if model is available, download if needed
        final manager = mlkit.EntityExtractorModelManager();
        const languageTag = 'en'; // Use language tag instead of enum
        final isDownloaded = await manager.isModelDownloaded(languageTag);

        if (!isDownloaded) {
          print('Downloading ML Kit model...');
          await manager.downloadModel(languageTag);
          print('ML Kit model downloaded successfully');
        }
      } else {
        print('Skipping model download check (cached as downloaded)');
      }

      _entityExtractor = mlkit.EntityExtractor(language: mlkit.EntityExtractorLanguage.english);
      _isInitialized = true;
      print('Real ML Kit Entity Extractor initialized successfully');
    } catch (e) {
      print('Failed to initialize Real ML Kit Entity Extractor: $e');
      _isInitialized = false;
      rethrow;
    }
  }

  @override
  Future<List<EntityAnnotationBase>> annotateText(String text) async {
    if (!_isInitialized || _entityExtractor == null) {
      await initialize();
    }

    if (_entityExtractor == null) {
      throw Exception('ML Kit Entity Extractor not initialized');
    }

    try {
      final entities = await _entityExtractor!.annotateText(text);
      return entities.map((entity) => RealEntityAnnotation(entity)).toList();
    } catch (e) {
      print('Error during ML Kit entity annotation: $e');
      rethrow;
    }
  }

  @override
  Future<void> close() async {
    if (_entityExtractor != null) {
      await _entityExtractor!.close();
      _entityExtractor = null;
    }
    _isInitialized = false;
  }

  @override
  bool get isInitialized => _isInitialized && _entityExtractor != null;
}
