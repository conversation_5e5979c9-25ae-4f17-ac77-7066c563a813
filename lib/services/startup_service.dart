import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

import 'storage_service.dart';
import 'parser/mlkit_parser_service.dart';
import '../models/transaction_model.dart';
import '../theme.dart';

/// Enumeration of possible service initialization states
enum ServiceInitializationState {
  pending,
  loading,
  ready,
  failed,
}

/// Data class to track service initialization status
class ServiceStatus {
  final ServiceInitializationState state;
  final String? error;
  final DateTime? startTime;
  final DateTime? endTime;

  ServiceStatus({
    required this.state,
    this.error,
    this.startTime,
    this.endTime,
  });

  Duration? get duration {
    if (startTime != null && endTime != null) {
      return endTime!.difference(startTime!);
    }
    return null;
  }

  ServiceStatus copyWith({
    ServiceInitializationState? state,
    String? error,
    DateTime? startTime,
    DateTime? endTime,
  }) {
    return ServiceStatus(
      state: state ?? this.state,
      error: error ?? this.error,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
    );
  }
}

/// Singleton service that manages the progressive initialization of all app services
class StartupService extends ChangeNotifier {
  static StartupService? _instance;
  
  final StorageService _storageService;
  final Map<String, ServiceStatus> _serviceStatuses = {};
  final StreamController<Map<String, ServiceStatus>> _statusController = 
      StreamController<Map<String, ServiceStatus>>.broadcast();
  
  // Service instances
  MlKitParserService? _mlKitService;
  TransactionProvider? _transactionProvider;
  ThemeProvider? _themeProvider;
  
  // Configuration
  static const Duration _initializationTimeout = Duration(minutes: 2);
  static const int _maxRetryAttempts = 3;
  
  StartupService._(this._storageService) {
    _initializeServiceStatuses();
  }

  /// Get singleton instance
  static StartupService getInstance(StorageService storageService) {
    _instance ??= StartupService._(storageService);
    return _instance!;
  }

  /// Reset singleton instance (for testing)
  static void resetInstance() {
    _instance?._statusController.close();
    _instance = null;
  }

  // Getters for service instances
  MlKitParserService? get mlKitService => _mlKitService;
  TransactionProvider? get transactionProvider => _transactionProvider;
  ThemeProvider? get themeProvider => _themeProvider;

  // Status getters
  Map<String, ServiceStatus> get serviceStatuses => Map.unmodifiable(_serviceStatuses);
  Stream<Map<String, ServiceStatus>> get statusStream => _statusController.stream;
  
  bool get isAllServicesReady => _serviceStatuses.values
      .every((status) => status.state == ServiceInitializationState.ready);
  
  bool get hasAnyServiceFailed => _serviceStatuses.values
      .any((status) => status.state == ServiceInitializationState.failed);

  /// Initialize service status tracking
  void _initializeServiceStatuses() {
    _serviceStatuses['theme'] = ServiceStatus(state: ServiceInitializationState.pending);
    _serviceStatuses['transaction'] = ServiceStatus(state: ServiceInitializationState.pending);
    _serviceStatuses['mlkit'] = ServiceStatus(state: ServiceInitializationState.pending);
  }

  /// Initialize all services progressively
  Future<void> initializeAllServices() async {
    developer.log('Starting progressive service initialization', name: 'StartupService');
    
    try {
      // Initialize services in order of dependency and importance
      await _initializeWithRetry('theme', _initializeThemeProvider);
      await _initializeWithRetry('transaction', _initializeTransactionProvider);
      await _initializeWithRetry('mlkit', _initializeMlKitService);
      
      developer.log('All services initialized successfully', name: 'StartupService');
    } catch (e) {
      developer.log('Service initialization completed with errors: $e', name: 'StartupService');
    }
    
    notifyListeners();
  }

  /// Initialize a service with retry logic and timeout
  Future<void> _initializeWithRetry(String serviceName, Future<void> Function() initializer) async {
    int attempts = 0;
    
    while (attempts < _maxRetryAttempts) {
      attempts++;
      
      try {
        _updateServiceStatus(serviceName, ServiceInitializationState.loading, startTime: DateTime.now());
        
        await initializer().timeout(_initializationTimeout);
        
        _updateServiceStatus(serviceName, ServiceInitializationState.ready, endTime: DateTime.now());
        developer.log('$serviceName service initialized successfully (attempt $attempts)', name: 'StartupService');
        return;
        
      } catch (e) {
        final errorMessage = 'Failed to initialize $serviceName service (attempt $attempts): $e';
        developer.log(errorMessage, name: 'StartupService');
        
        if (attempts >= _maxRetryAttempts) {
          _updateServiceStatus(serviceName, ServiceInitializationState.failed, 
              error: errorMessage, endTime: DateTime.now());
          // Don't rethrow - allow other services to continue initializing
          return;
        }
        
        // Wait before retry
        await Future.delayed(Duration(milliseconds: 500 * attempts));
      }
    }
  }

  /// Initialize theme provider
  Future<void> _initializeThemeProvider() async {
    _themeProvider = ThemeProvider();
    await _themeProvider!.initialize();
  }

  /// Initialize transaction provider
  Future<void> _initializeTransactionProvider() async {
    _transactionProvider = TransactionProvider(_storageService);
    await _transactionProvider!.initialize();
  }

  /// Initialize ML Kit service in background
  Future<void> _initializeMlKitService() async {
    try {
      _mlKitService = await MlKitParserService.initializeInBackground(_storageService);
    } catch (e) {
      // ML Kit failure is not critical - app can work without it
      developer.log('ML Kit service initialization failed, app will use fallback parsing: $e', name: 'StartupService');
      rethrow;
    }
  }

  /// Update service status and notify listeners
  void _updateServiceStatus(String serviceName, ServiceInitializationState state, 
      {String? error, DateTime? startTime, DateTime? endTime}) {
    final currentStatus = _serviceStatuses[serviceName]!;
    _serviceStatuses[serviceName] = currentStatus.copyWith(
      state: state,
      error: error,
      startTime: startTime ?? currentStatus.startTime,
      endTime: endTime,
    );
    
    _statusController.add(Map.unmodifiable(_serviceStatuses));
    notifyListeners();
  }

  /// Check if a specific service is ready
  bool isServiceReady(String serviceName) {
    return _serviceStatuses[serviceName]?.state == ServiceInitializationState.ready;
  }

  /// Get initialization duration for a service
  Duration? getServiceInitializationDuration(String serviceName) {
    return _serviceStatuses[serviceName]?.duration;
  }

  /// Get total initialization time
  Duration? getTotalInitializationTime() {
    final durations = _serviceStatuses.values
        .map((status) => status.duration)
        .where((duration) => duration != null)
        .cast<Duration>();
    
    if (durations.isEmpty) return null;
    
    return durations.reduce((a, b) => a + b);
  }

  /// Retry failed service initialization
  Future<void> retryFailedServices() async {
    final failedServices = _serviceStatuses.entries
        .where((entry) => entry.value.state == ServiceInitializationState.failed)
        .map((entry) => entry.key)
        .toList();
    
    for (final serviceName in failedServices) {
      switch (serviceName) {
        case 'theme':
          await _initializeWithRetry('theme', _initializeThemeProvider);
          break;
        case 'transaction':
          await _initializeWithRetry('transaction', _initializeTransactionProvider);
          break;
        case 'mlkit':
          await _initializeWithRetry('mlkit', _initializeMlKitService);
          break;
      }
    }
  }

  @override
  void dispose() {
    _statusController.close();
    super.dispose();
  }
}
