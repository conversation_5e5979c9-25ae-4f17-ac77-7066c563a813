import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../lib/services/startup_service.dart';
import '../../lib/theme.dart';
import '../../lib/models/transaction_model.dart';
import '../mocks/mock_storage_service.dart';

/// Unit tests for measuring and validating startup performance
void main() {
  group('Startup Performance Tests', () {
    late MockStorageService mockStorage;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
    });

    tearDown(() {
      StartupService.resetInstance();
      mockStorage.reset();
    });

    test('StartupService creation performance', () {
      final startTime = DateTime.now();

      // Create startup service
      final startupService = StartupService.getInstance(mockStorage);

      final endTime = DateTime.now();
      final creationDuration = endTime.difference(startTime);

      // Service creation should be fast
      expect(creationDuration.inMilliseconds, lessThan(100),
          reason: 'StartupService creation should be under 100ms');

      // Verify service is created
      expect(startupService, isNotNull);

      print('StartupService created in ${creationDuration.inMilliseconds}ms');
    });

    test('ThemeProvider initialization performance', () async {
      final startTime = DateTime.now();

      // Create and initialize theme provider
      final themeProvider = ThemeProvider();
      await themeProvider.initialize();

      final endTime = DateTime.now();
      final initDuration = endTime.difference(startTime);

      // Theme initialization should be fast
      expect(initDuration.inMilliseconds, lessThan(500),
          reason: 'ThemeProvider initialization should be under 500ms');

      print('ThemeProvider initialized in ${initDuration.inMilliseconds}ms');
    });

    test('TransactionProvider initialization performance', () async {
      final startTime = DateTime.now();

      // Create and initialize transaction provider
      final transactionProvider = TransactionProvider(mockStorage);
      await transactionProvider.initialize();

      final endTime = DateTime.now();
      final initDuration = endTime.difference(startTime);

      // Transaction initialization should be reasonable
      expect(initDuration.inMilliseconds, lessThan(2000),
          reason: 'TransactionProvider initialization should be under 2 seconds');

      print('TransactionProvider initialized in ${initDuration.inMilliseconds}ms');
    });

    test('Caching flags performance', () async {
      final startTime = DateTime.now();

      // Test setting cache flags
      await mockStorage.setBool('ml_kit_model_downloaded', true);
      await mockStorage.setBool('learned_association_migration_completed', true);

      // Test reading cache flags
      final mlKitCached = mockStorage.getBool('ml_kit_model_downloaded');
      final migrationCached = mockStorage.getBool('learned_association_migration_completed');

      final endTime = DateTime.now();
      final cacheDuration = endTime.difference(startTime);

      // Cache operations should be very fast
      expect(cacheDuration.inMilliseconds, lessThan(50),
          reason: 'Cache operations should be under 50ms');

      // Verify cache values
      expect(mlKitCached, isTrue);
      expect(migrationCached, isTrue);

      print('Cache operations completed in ${cacheDuration.inMilliseconds}ms');
    });
  });
}
