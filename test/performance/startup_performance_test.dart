import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:provider/provider.dart';

import '../../lib/main.dart' as app;
import '../../lib/services/startup_service.dart';
import '../../lib/services/storage_service.dart';
import '../mocks/mock_storage_service.dart';

/// Integration tests for measuring and validating startup performance
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Startup Performance Tests', () {
    late MockStorageService mockStorage;
    late StartupService startupService;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
      startupService = StartupService.getInstance(mockStorage);
    });

    tearDown(() {
      StartupService.resetInstance();
    });

    testWidgets('Cold start performance - first launch', (WidgetTester tester) async {
      final startTime = DateTime.now();
      
      // Simulate cold start by ensuring no cached data
      await mockStorage.setBool('ml_kit_model_downloaded', false);
      await mockStorage.setBool('learned_association_migration_completed', false);
      
      // Start the app
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<StartupService>.value(value: startupService),
            Provider<ThemeProvider?>.value(value: null),
            Provider<TransactionProvider?>.value(value: null),
            Provider<MlKitParserService?>.value(value: null),
          ],
          child: const MaterialApp(home: Scaffold(body: Text('Test App'))),
        ),
      );

      // Trigger service initialization
      await startupService.initializeAllServices();
      await tester.pumpAndSettle();

      final endTime = DateTime.now();
      final startupDuration = endTime.difference(startTime);

      // Validate startup time is within acceptable limits for cold start
      expect(startupDuration.inMilliseconds, lessThan(10000), // 10 seconds max for cold start
          reason: 'Cold start should complete within 10 seconds');

      // Verify all services are initialized
      expect(startupService.isAllServicesReady, isTrue,
          reason: 'All services should be ready after initialization');

      print('Cold start completed in ${startupDuration.inMilliseconds}ms');
    });

    testWidgets('Warm start performance - subsequent launch', (WidgetTester tester) async {
      // Simulate warm start by pre-caching data
      await mockStorage.setBool('ml_kit_model_downloaded', true);
      await mockStorage.setBool('learned_association_migration_completed', true);
      
      final startTime = DateTime.now();
      
      // Start the app
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<StartupService>.value(value: startupService),
            Provider<ThemeProvider?>.value(value: null),
            Provider<TransactionProvider?>.value(value: null),
            Provider<MlKitParserService?>.value(value: null),
          ],
          child: const MaterialApp(home: Scaffold(body: Text('Test App'))),
        ),
      );

      // Trigger service initialization
      await startupService.initializeAllServices();
      await tester.pumpAndSettle();

      final endTime = DateTime.now();
      final startupDuration = endTime.difference(startTime);

      // Validate startup time is within acceptable limits for warm start
      expect(startupDuration.inMilliseconds, lessThan(3000), // 3 seconds max for warm start
          reason: 'Warm start should complete within 3 seconds');

      // Verify all services are initialized
      expect(startupService.isAllServicesReady, isTrue,
          reason: 'All services should be ready after initialization');

      print('Warm start completed in ${startupDuration.inMilliseconds}ms');
    });

    testWidgets('Service initialization order and timing', (WidgetTester tester) async {
      final initializationTimes = <String, DateTime>{};
      
      // Monitor service initialization
      startupService.statusStream.listen((statuses) {
        for (final entry in statuses.entries) {
          final serviceName = entry.key;
          final status = entry.value;
          
          if (status.state == ServiceInitializationState.ready && 
              !initializationTimes.containsKey(serviceName)) {
            initializationTimes[serviceName] = DateTime.now();
          }
        }
      });

      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<StartupService>.value(value: startupService),
            Provider<ThemeProvider?>.value(value: null),
            Provider<TransactionProvider?>.value(value: null),
            Provider<MlKitParserService?>.value(value: null),
          ],
          child: const MaterialApp(home: Scaffold(body: Text('Test App'))),
        ),
      );

      final startTime = DateTime.now();
      await startupService.initializeAllServices();
      await tester.pumpAndSettle();

      // Verify initialization order (theme should be first, then transaction, then mlkit)
      expect(initializationTimes.containsKey('theme'), isTrue);
      expect(initializationTimes.containsKey('transaction'), isTrue);
      
      // Theme should initialize before transaction
      if (initializationTimes['theme'] != null && initializationTimes['transaction'] != null) {
        expect(initializationTimes['theme']!.isBefore(initializationTimes['transaction']!), 
            isTrue, reason: 'Theme should initialize before transaction service');
      }

      // Print timing information
      for (final entry in initializationTimes.entries) {
        final duration = entry.value.difference(startTime);
        print('${entry.key} service initialized in ${duration.inMilliseconds}ms');
      }
    });

    testWidgets('Error handling and fallback performance', (WidgetTester tester) async {
      // Simulate ML Kit initialization failure
      await mockStorage.setBool('ml_kit_model_downloaded', false);
      
      final startTime = DateTime.now();
      
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<StartupService>.value(value: startupService),
            Provider<ThemeProvider?>.value(value: null),
            Provider<TransactionProvider?>.value(value: null),
            Provider<MlKitParserService?>.value(value: null),
          ],
          child: const MaterialApp(home: Scaffold(body: Text('Test App'))),
        ),
      );

      await startupService.initializeAllServices();
      await tester.pumpAndSettle();

      final endTime = DateTime.now();
      final startupDuration = endTime.difference(startTime);

      // Even with ML Kit failure, app should start within reasonable time
      expect(startupDuration.inMilliseconds, lessThan(15000), // 15 seconds max with failures
          reason: 'App should start within 15 seconds even with service failures');

      // Essential services should still be ready
      expect(startupService.isServiceReady('theme'), isTrue,
          reason: 'Theme service should be ready');
      expect(startupService.isServiceReady('transaction'), isTrue,
          reason: 'Transaction service should be ready');

      print('Startup with errors completed in ${startupDuration.inMilliseconds}ms');
    });

    testWidgets('Memory usage during startup', (WidgetTester tester) async {
      // This test would require platform-specific memory monitoring
      // For now, we'll just ensure the app doesn't crash during startup
      
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<StartupService>.value(value: startupService),
            Provider<ThemeProvider?>.value(value: null),
            Provider<TransactionProvider?>.value(value: null),
            Provider<MlKitParserService?>.value(value: null),
          ],
          child: const MaterialApp(home: Scaffold(body: Text('Test App'))),
        ),
      );

      await startupService.initializeAllServices();
      await tester.pumpAndSettle();

      // Verify app is still responsive
      expect(find.text('Test App'), findsOneWidget);
      
      // Verify no memory leaks by checking service instances
      expect(startupService.themeProvider, isNotNull);
      expect(startupService.transactionProvider, isNotNull);
    });

    testWidgets('Startup performance regression detection', (WidgetTester tester) async {
      const maxAcceptableStartupTime = 5000; // 5 seconds baseline
      
      final startTime = DateTime.now();
      
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<StartupService>.value(value: startupService),
            Provider<ThemeProvider?>.value(value: null),
            Provider<TransactionProvider?>.value(value: null),
            Provider<MlKitParserService?>.value(value: null),
          ],
          child: const MaterialApp(home: Scaffold(body: Text('Test App'))),
        ),
      );

      await startupService.initializeAllServices();
      await tester.pumpAndSettle();

      final endTime = DateTime.now();
      final startupDuration = endTime.difference(startTime);

      // Fail test if startup time exceeds baseline
      expect(startupDuration.inMilliseconds, lessThan(maxAcceptableStartupTime),
          reason: 'Startup time regression detected! Current: ${startupDuration.inMilliseconds}ms, Max: ${maxAcceptableStartupTime}ms');

      print('Startup performance check passed: ${startupDuration.inMilliseconds}ms');
    });
  });
}
