I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I've analyzed the Money Lover Chat Flutter app's startup performance bottlenecks. The main issues are:

1. **ML Kit Model Download**: The `RealEntityExtractor.initialize()` method synchronously downloads ML Kit models during startup, which can take seconds or minutes on first launch
2. **Duplicate Service Initialization**: `LearnedAssociationService` is initialized twice - once in `MlKitParserService.getInstance()` and again in `TransactionProvider`
3. **Synchronous Data Loading**: `TransactionProvider` loads all transactions, messages, and categories from SharedPreferences during startup
4. **Theme Provider**: Also loads preferences synchronously during initialization

The app blocks the UI thread until all these operations complete, causing slow startup times.

### Approach

The solution involves moving heavy initialization operations behind the first frame render and implementing progressive loading:

1. **Defer ML Kit Initialization**: Move ML Kit service initialization to background after UI is shown
2. **Eliminate Duplicate Service Calls**: Reuse the same service instances across providers
3. **Implement Progressive Data Loading**: Show UI immediately with loading states, then load data asynchronously
4. **Add Startup Performance Monitoring**: Instrument the startup flow to measure and track improvements
5. **Cache Initialization Flags**: Prevent redundant checks on subsequent app launches

### Reasoning

I explored the codebase systematically to understand the startup flow. I examined the main entry point in `main.dart` and traced through all synchronous operations that block the UI. I analyzed the ML Kit service initialization, storage service patterns, and provider setup. I also checked the navigation structure, theme provider, and main screens to identify all potential bottlenecks during app startup.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant Main as main()
    participant Storage as StorageService
    participant UI as App UI
    participant Startup as StartupService
    participant MLKit as MlKitParserService
    participant Provider as TransactionProvider
    participant Theme as ThemeProvider

    User->>Main: Launch App
    Main->>Storage: init() (fast)
    Storage-->>Main: Ready
    Main->>UI: runApp() - Show UI immediately
    UI->>User: Display loading screen
    
    par Background Initialization
        UI->>Startup: initializeServices()
        Startup->>Theme: initialize() (async)
        Theme->>Storage: load preferences
        Theme-->>Startup: Ready
        
        Startup->>Provider: initialize() (async)
        Provider->>Storage: load transactions/messages
        Provider-->>Startup: Ready
        
        Startup->>MLKit: initializeInBackground() (async)
        MLKit->>Storage: check model downloaded flag
        alt Model not downloaded
            MLKit->>MLKit: download model (slow)
        end
        MLKit-->>Startup: Ready
    end
    
    Startup-->>UI: All services ready
    UI->>User: Show main app interface

## Proposed File Changes

### lib/main.dart(MODIFY)

References: 

- lib/services/storage_service.dart
- lib/services/parser/mlkit_parser_service.dart(MODIFY)

Refactor the main function to defer heavy initialization operations:

1. Remove the `await MlKitParserService.getInstance(storageService)` call from main function
2. Keep only the essential `StorageService().init()` call that's needed for basic app functionality
3. Update the MultiProvider setup to handle nullable `MlKitParserService` using `Provider<MlKitParserService?>.value(value: null)` initially
4. Add startup performance logging using `Timeline.timeSync` or simple DateTime logging to measure each initialization step
5. Add a flag to track when ML Kit service becomes available

The main function should show the UI immediately after storage service initialization, then trigger background initialization of heavy services.

### lib/models/transaction_model.dart(MODIFY)

References: 

- lib/services/storage_service.dart
- lib/services/parser/learned_association_service.dart(MODIFY)

Optimize TransactionProvider initialization to support progressive loading:

1. Modify the constructor to not call `_loadData()` synchronously - instead set initial loading state
2. Add a `bool _isInitializing = true` field and expose it as a getter
3. Create a new `Future<void> initialize()` method that performs the data loading asynchronously
4. Update `_initializeLearnedAssociationService()` to reuse the service instance from the main provider instead of creating a new one
5. Add error handling and retry logic for data loading failures
6. Implement skeleton/loading states for when data is not yet available
7. Add a method to get the service instance from the main provider to avoid duplicate initialization

This allows the provider to be created immediately while data loads in the background.

### lib/theme.dart(MODIFY)

Optimize ThemeProvider initialization to be non-blocking:

1. Modify the constructor to not call `_loadThemePreference()` synchronously
2. Set a default theme initially (light mode) so the app can render immediately
3. Create a `Future<void> initialize()` method that loads the theme preference asynchronously
4. Add a loading state to prevent theme flickering during initialization
5. Call `notifyListeners()` only after the preference is loaded to update the UI

This ensures the theme provider doesn't block startup while still loading user preferences.

### lib/services/parser/mlkit_parser_service.dart(MODIFY)

References: 

- lib/services/storage_service.dart
- lib/services/parser/real_entity_extractor.dart

Add caching and optimization to ML Kit service initialization:

1. Add a persistent flag in SharedPreferences to track if the ML Kit model has been downloaded (`ml_kit_model_downloaded`)
2. Skip the `isModelDownloaded()` check on subsequent app launches if the flag is set
3. Add a static method `initializeInBackground()` that can be called after the UI is shown
4. Implement proper error handling and fallback when ML Kit is not available
5. Add logging to track initialization time and success/failure
6. Ensure the service gracefully handles being used before full initialization
7. Add a `isReady` getter to check if the service is fully initialized

This reduces the ML Kit initialization overhead on subsequent app launches.

### lib/services/parser/learned_association_service.dart(MODIFY)

References: 

- lib/services/storage_service.dart

Optimize learned association service to prevent duplicate initialization:

1. Add a persistent flag in SharedPreferences to track if migration has been completed (`learned_association_migration_completed`)
2. Skip the migration check on subsequent app launches if the flag is set
3. Add a static method to check if an instance already exists before creating a new one
4. Implement lazy loading for the migration process - only run it when actually needed
5. Add performance logging to track migration time
6. Optimize the JSON parsing and storage operations for large datasets

This prevents the expensive migration from running on every app launch.

### lib/screens/chat_screen.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- lib/services/storage_service.dart

Update ChatScreen to handle progressive service initialization:

1. Remove the duplicate `_initializeParserService()` call that creates another StorageService and MlKitParserService instance
2. Use the service instances from the Provider instead of creating new ones
3. Add proper null checking for when MlKitParserService is not yet available
4. Show appropriate loading states or fallback messages when services are initializing
5. Listen for service availability changes and update the UI accordingly
6. Handle the case where ML Kit service becomes available after the screen is already loaded
7. Add error handling for when services fail to initialize

This eliminates duplicate service initialization and provides better user feedback during startup.

### lib/navigation/app_navigation.dart(MODIFY)

References: 

- lib/models/transaction_model.dart(MODIFY)
- lib/theme.dart(MODIFY)

Add initialization coordination to the main navigation:

1. Add a splash/loading state that shows while services are initializing
2. Coordinate the initialization of ThemeProvider, TransactionProvider, and MlKitParserService
3. Show the main navigation only after essential services are ready
4. Add error handling and retry mechanisms for failed initialization
5. Provide user feedback about initialization progress
6. Implement a timeout mechanism to prevent indefinite loading

This provides a smooth user experience during the progressive loading phase.

### lib/services/startup_service.dart(NEW)

References: 

- lib/services/storage_service.dart
- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- lib/models/transaction_model.dart(MODIFY)

Create a new startup coordination service:

1. Create a singleton service that manages the progressive initialization of all app services
2. Implement methods to initialize services in the correct order and handle dependencies
3. Provide status tracking for each service initialization (pending, loading, ready, failed)
4. Add performance monitoring and logging for startup metrics
5. Implement retry logic for failed service initialization
6. Provide a stream or notifier for UI components to listen to initialization progress
7. Add methods to check if specific services are ready before using them
8. Include timeout handling to prevent indefinite loading states

This centralizes startup coordination and provides better visibility into the initialization process.

### test/performance/startup_performance_test.dart(NEW)

References: 

- lib/main.dart(MODIFY)

Create a startup performance test:

1. Implement an integration test that measures app startup time from launch to first frame
2. Add benchmarks for each initialization phase (storage, ML Kit, data loading)
3. Test both cold start (first launch) and warm start (subsequent launches) scenarios
4. Add assertions to ensure startup time stays within acceptable limits
5. Test on different device configurations and data volumes
6. Include tests for error scenarios and fallback behavior
7. Add performance regression detection

This ensures startup performance improvements are maintained and prevents regressions.

### lib/widgets/startup_loading_widget.dart(NEW)

Create a startup loading widget:

1. Design a loading screen that shows during service initialization
2. Include progress indicators for different initialization phases
3. Add branded loading animations and messaging
4. Implement error states with retry buttons
5. Show helpful tips or onboarding content during loading
6. Add accessibility support for screen readers
7. Implement smooth transitions to the main app once loading is complete

This provides a polished user experience during the startup process.

### docs/performance_optimization.md(NEW)

Create documentation for the startup performance optimization:

1. Document the startup performance issues that were identified
2. Explain the progressive loading approach and its benefits
3. Provide guidelines for adding new services without impacting startup time
4. Document the performance monitoring and testing approach
5. Include troubleshooting guide for startup-related issues
6. Add metrics and benchmarks for different device types
7. Provide best practices for maintaining good startup performance

This helps future developers understand and maintain the startup optimizations.